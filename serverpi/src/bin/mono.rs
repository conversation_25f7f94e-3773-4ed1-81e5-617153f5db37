//! Catapult Control System with HTTP API and State Machine
//! Implements Armed -> Launched -> Disarmed state cycle
//! PIN_25: Armed indicator (high when armed)
//! PIN_10: Launch indicator (high when launched)

#![no_std]
#![no_main]

use core::str;
use defmt::*;
use embassy_executor::Spawner;
use embassy_futures::{join::join4, yield_now};
use embassy_net::{Stack, StackResources};
use embassy_net_wiznet::chip::W5500;
use embassy_net_wiznet::{<PERSON><PERSON>, Runner, State as WizState};
use embassy_rp::bind_interrupts;
use embassy_rp::clocks::RoscRng;
use embassy_rp::gpio::{Input, Level, Output, Pull};
use embassy_rp::peripherals::{SPI0, USB};
use embassy_rp::spi::{Async, Config as SpiConfig, Spi};
use embassy_rp::usb::{<PERSON>, InterruptHandler};
use embassy_sync::{blocking_mutex::raw::ThreadModeRawMutex, mutex::Mutex};
use embassy_time::{Delay, Duration, Instant, Timer};
use embassy_usb::class::cdc_acm::{CdcAcmClass, State as UsbState};
use embassy_usb::{Builder, Config};
use embedded_hal_bus::spi::ExclusiveDevice;
use embedded_io_async::Write;
use heapless::{String, Vec};
use static_cell::StaticCell;
use {defmt_rtt as _, panic_probe as _};

bind_interrupts!(struct Irqs {
    USBCTRL_IRQ => InterruptHandler<USB>;
});

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum CatapultState {
    Disarmed,
    Armed,
    Launching,
}

impl CatapultState {
    pub fn as_str(&self) -> &'static str {
        match self {
            CatapultState::Disarmed => "disarmed",
            CatapultState::Armed => "armed",
            CatapultState::Launching => "launching",
        }
    }
}

pub struct CatapultController {
    state: CatapultState,
    armed_pin: Output<'static>,  // PIN_25
    launch_pin: Output<'static>, // PIN_10
    launch_start_time: Option<Instant>,
    launch_led_state: bool,
}

impl CatapultController {
    pub fn new(armed_pin: Output<'static>, launch_pin: Output<'static>) -> Self {
        let mut controller = Self {
            state: CatapultState::Disarmed,
            armed_pin,
            launch_pin,
            launch_start_time: None,
            launch_led_state: false,
        };
        controller.update_pins();
        controller
    }

    pub fn get_state(&self) -> CatapultState {
        self.state
    }

    pub fn arm(&mut self) -> Result<(), &'static str> {
        match self.state {
            CatapultState::Disarmed => {
                self.state = CatapultState::Armed;
                self.update_pins();
                log::info!("Catapult ARMED");
                Ok(())
            }
            _ => Err("Cannot arm: not in disarmed state"),
        }
    }

    pub fn launch(&mut self) -> Result<(), &'static str> {
        match self.state {
            CatapultState::Armed => {
                self.state = CatapultState::Launching;
                self.launch_start_time = Some(Instant::now());
                self.launch_led_state = false;
                self.update_pins();
                log::info!("Catapult LAUNCHING - Motor started");
                Ok(())
            }
            CatapultState::Launching => Err("Already launching"),
            CatapultState::Disarmed => Err("Cannot launch: not armed"),
        }
    }

    pub fn stop(&mut self) -> Result<&'static str, &'static str> {
        match self.state {
            CatapultState::Armed => {
                self.state = CatapultState::Disarmed;
                self.launch_start_time = None;
                self.update_pins();
                log::info!("Catapult DISARMED");
                Ok("Catapult disarmed")
            }
            CatapultState::Launching => {
                self.state = CatapultState::Disarmed;
                self.launch_start_time = None;
                self.update_pins();
                log::info!("Catapult LAUNCH ABORTED - Motor stopped");
                Ok("Launch aborted")
            }
            CatapultState::Disarmed => Err("Already disarmed"),
        }
    }

    fn update_pins(&mut self) {
        match self.state {
            CatapultState::Disarmed => {
                self.armed_pin.set_low();
                self.launch_pin.set_low();
            }
            CatapultState::Armed => {
                self.armed_pin.set_high();
                self.launch_pin.set_low();
            }
            CatapultState::Launching => {
                self.armed_pin.set_low();
                // For launching, we'll control the pin in the blink function
                // to simulate motor PWM control
                if self.launch_led_state {
                    self.launch_pin.set_high();
                } else {
                    self.launch_pin.set_low();
                }
            }
        }
    }

    pub fn update_launch_cycle(&mut self) -> bool {
        if let CatapultState::Launching = self.state {
            if let Some(start_time) = self.launch_start_time {
                let elapsed = start_time.elapsed();

                // Launch sequence lasts 10 seconds
                if elapsed >= Duration::from_secs(10) {
                    // Auto-complete launch sequence
                    self.state = CatapultState::Disarmed;
                    self.launch_start_time = None;
                    self.update_pins();
                    log::info!("Catapult LAUNCH COMPLETE - Motor stopped");
                    return true;
                }

                // Blink LED every 200ms to simulate motor operation
                let blink_cycle = elapsed.as_millis() / 200;
                let new_led_state = (blink_cycle % 2) == 0;

                if new_led_state != self.launch_led_state {
                    self.launch_led_state = new_led_state;
                    self.update_pins();
                }
            }
        }
        false
    }
}

type SharedController = Mutex<ThreadModeRawMutex, CatapultController>;
static CATAPULT: StaticCell<SharedController> = StaticCell::new();

#[embassy_executor::task]
async fn ethernet_task(
    runner: Runner<
        'static,
        W5500,
        ExclusiveDevice<Spi<'static, SPI0, Async>, Output<'static>, Delay>,
        Input<'static>,
        Output<'static>,
    >,
) -> ! {
    runner.run().await
}

#[embassy_executor::task]
async fn net_task(mut runner: embassy_net::Runner<'static, Device<'static>>) -> ! {
    runner.run().await
}

#[embassy_executor::task]
async fn state_machine_task(catapult: &'static SharedController) -> ! {
    loop {
        {
            let mut controller = catapult.lock().await;
            let _state_changed = controller.update_launch_cycle();
        }
        Timer::after_millis(50).await; // Check every 50ms for responsive control
    }
}

#[embassy_executor::main]
async fn main(spawner: Spawner) {
    log::info!("Starting Catapult Control System...");

    let p = embassy_rp::init(Default::default());
    let mut rng = RoscRng;

    // Initialize catapult controller with GPIO pins
    let armed_pin = Output::new(p.PIN_25, Level::Low); // Armed indicator
    let launch_pin = Output::new(p.PIN_10, Level::Low); // Launch indicator
    let controller = CatapultController::new(armed_pin, launch_pin);
    let catapult = CATAPULT.init(Mutex::new(controller));

    // Setup USB Serial Logger
    let driver = Driver::new(p.USB, Irqs);
    let mut usb_config = Config::new(0xc0de, 0xcafe);
    usb_config.manufacturer = Some("Embassy");
    usb_config.product = Some("Catapult Controller");
    usb_config.serial_number = Some("12345678");
    usb_config.max_power = 100;
    usb_config.max_packet_size_0 = 64;

    let mut config_descriptor = [0; 256];
    let mut bos_descriptor = [0; 256];
    let mut control_buf = [0; 64];
    let mut logger_state = UsbState::new();

    let mut builder = Builder::new(
        driver,
        usb_config,
        &mut config_descriptor,
        &mut bos_descriptor,
        &mut [],
        &mut control_buf,
    );

    let logger_class = CdcAcmClass::new(&mut builder, &mut logger_state, 64);
    let log_fut = embassy_usb_logger::with_class!(1024, log::LevelFilter::Info, logger_class);
    let mut usb = builder.build();

    // Setup Ethernet
    let mut spi_cfg = SpiConfig::default();
    spi_cfg.frequency = 50_000_000;
    let (miso, mosi, clk) = (p.PIN_16, p.PIN_19, p.PIN_18);
    let spi = Spi::new(p.SPI0, clk, mosi, miso, p.DMA_CH0, p.DMA_CH1, spi_cfg);
    let cs = Output::new(p.PIN_17, Level::High);
    let w5500_int = Input::new(p.PIN_21, Pull::Up);
    let w5500_reset = Output::new(p.PIN_20, Level::High);

    let mac_addr = [0x02, 0x00, 0x00, 0x00, 0x00, 0x00];
    static NET_STATE: StaticCell<WizState<8, 8>> = StaticCell::new();
    let net_state = NET_STATE.init(WizState::<8, 8>::new());
    let (device, eth_runner) = embassy_net_wiznet::new(
        mac_addr,
        net_state,
        ExclusiveDevice::new(spi, cs, Delay),
        w5500_int,
        w5500_reset,
    )
    .await
    .unwrap();
    unwrap!(spawner.spawn(ethernet_task(eth_runner)));

    // Generate random seed
    let seed = rng.next_u64();

    // Init network stack
    static RESOURCES: StaticCell<StackResources<3>> = StaticCell::new();
    let (stack, net_runner) = embassy_net::new(
        device,
        embassy_net::Config::dhcpv4(Default::default()),
        RESOURCES.init(StackResources::new()),
        seed,
    );

    unwrap!(spawner.spawn(net_task(net_runner)));
    unwrap!(spawner.spawn(state_machine_task(catapult)));

    // HTTP server task
    let http_task = async {
        log::info!("Waiting for DHCP...");
        let cfg = wait_for_config(stack).await;
        let local_addr = cfg.address.address();
        log::info!("IP address: {:?}", local_addr);
        log::info!("HTTP API available at: http://{:?}", local_addr);

        let mut rx_buffer = [0; 4096];
        let mut tx_buffer = [0; 4096];
        let mut buf = [0; 4096];

        loop {
            let mut socket =
                embassy_net::tcp::TcpSocket::new(stack, &mut rx_buffer, &mut tx_buffer);
            socket.set_timeout(Some(Duration::from_secs(10)));

            log::info!("Listening on HTTP:80...");
            if let Err(e) = socket.accept(80).await {
                log::warn!("accept error: {:?}", e);
                continue;
            }
            log::info!("HTTP connection from {:?}", socket.remote_endpoint());

            if let Err(e) = handle_http_request(&mut socket, &mut buf, catapult).await {
                log::warn!("HTTP request error: {:?}", e);
            }
        }
    };

    let usb_fut = usb.run();
    join4(usb_fut, http_task, log_fut, core::future::pending::<()>()).await;
}

async fn handle_http_request(
    socket: &mut embassy_net::tcp::TcpSocket<'_>,
    buf: &mut [u8],
    catapult: &SharedController,
) -> Result<(), &'static str> {
    // Read the complete HTTP request
    let mut total_read = 0;
    loop {
        let n = socket
            .read(&mut buf[total_read..])
            .await
            .map_err(|_| "Read error")?;
        if n == 0 {
            break;
        }
        total_read += n;

        // Check if we have a complete HTTP request (ends with \r\n\r\n)
        let request_str = str::from_utf8(&buf[..total_read]).map_err(|_| "Invalid UTF-8")?;
        if request_str.contains("\r\n\r\n") {
            break;
        }

        // Prevent buffer overflow
        if total_read >= buf.len() - 100 {
            break;
        }
    }

    if total_read == 0 {
        return Err("Empty request");
    }

    let request = str::from_utf8(&buf[..total_read]).map_err(|_| "Invalid UTF-8")?;
    log::info!("HTTP Request: {}", request);

    // Parse HTTP request line
    let lines: Vec<&str, 10> = request.lines().collect();
    if lines.is_empty() {
        return Err("No request line");
    }

    let request_line = lines[0];
    let parts: Vec<&str, 3> = request_line.split_whitespace().collect();
    if parts.len() < 2 {
        return Err("Invalid request line");
    }

    let method = parts[0];
    let path = parts[1];

    log::info!("Method: {}, Path: {}", method, path);

    let response = match (method, path) {
        ("POST", "/arm") => {
            let mut controller = catapult.lock().await;
            match controller.arm() {
                Ok(()) => create_response(
                    "200 OK",
                    "application/json",
                    "{\"message\":\"Catapult armed successfully\"}",
                ),
                Err(e) => {
                    create_response("400 Bad Request", "application/json", &format_json_error(e))
                }
            }
        }
        ("POST", "/start") => {
            let mut controller = catapult.lock().await;
            match controller.launch() {
                Ok(()) => create_response(
                    "200 OK",
                    "application/json",
                    "{\"message\":\"Launch started\"}",
                ),
                Err(e) => {
                    create_response("400 Bad Request", "application/json", &format_json_error(e))
                }
            }
        }
        ("POST", "/stop") => {
            let mut controller = catapult.lock().await;
            match controller.stop() {
                Ok(message) => {
                    let response_body = format_json_message(message);
                    create_response("200 OK", "application/json", &response_body)
                }
                Err(e) => {
                    create_response("400 Bad Request", "application/json", &format_json_error(e))
                }
            }
        }
        ("GET", "/health") => {
            let controller = catapult.lock().await;
            let state = controller.get_state();
            let health_json = format_health_response(state);
            create_response("200 OK", "application/json", &health_json)
        }
        _ => create_response("404 Not Found", "text/plain", "Not Found"),
    };

    log::info!("Sending response: {}", &response[..response.len().min(100)]);

    // Send the response and ensure it's flushed
    if let Err(e) = socket.write_all(response.as_bytes()).await {
        log::error!("Write error: {:?}", e);
        return Err("Write error");
    }

    // Give some time for the response to be sent before closing
    Timer::after_millis(10).await;

    log::info!("Response sent successfully");
    Ok(())
}

fn create_response(status: &str, content_type: &str, body: &str) -> String<512> {
    let mut response = String::new();
    let _ = response.push_str("HTTP/1.1 ");
    let _ = response.push_str(status);
    let _ = response.push_str("\r\nContent-Type: ");
    let _ = response.push_str(content_type);
    let _ = response.push_str("\r\nContent-Length: ");

    // Convert body length to string manually since to_string() isn't available in no_std
    let body_len = body.len();
    let mut len_str = String::<16>::new();
    let mut temp = body_len;
    if temp == 0 {
        let _ = len_str.push('0');
    } else {
        let mut digits = Vec::<char, 16>::new();
        while temp > 0 {
            let _ = digits.push((b'0' + (temp % 10) as u8) as char);
            temp /= 10;
        }
        for digit in digits.iter().rev() {
            let _ = len_str.push(*digit);
        }
    }

    let _ = response.push_str(&len_str);
    let _ = response.push_str("\r\nConnection: close\r\n\r\n");
    let _ = response.push_str(body);
    response
}

fn format_json_message(message: &str) -> String<256> {
    let mut json = String::new();
    let _ = json.push_str("{\"message\":\"");
    let _ = json.push_str(message);
    let _ = json.push_str("\"}");
    json
}

fn format_json_error(error: &str) -> String<256> {
    let mut json = String::new();
    let _ = json.push_str("{\"error\":\"");
    let _ = json.push_str(error);
    let _ = json.push_str("\"}");
    json
}

fn format_health_response(state: CatapultState) -> String<256> {
    let mut json = String::new();
    let _ = json.push_str("{\"state\":\"");
    let _ = json.push_str(state.as_str());
    let _ = json.push_str("\",\"end_switch_closed\":false}");
    json
}

async fn wait_for_config(stack: Stack<'static>) -> embassy_net::StaticConfigV4 {
    loop {
        if let Some(config) = stack.config_v4() {
            return config.clone();
        }
        yield_now().await;
    }
}
