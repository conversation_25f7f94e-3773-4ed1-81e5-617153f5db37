//! This example implements a TCP echo server on port 1234 and using DHCP.
//! Send it some data, you should see it echoed back and printed in the console.
//!
//! Example written for the [`WIZnet W5500-EVB-Pico`](https://docs.wiznet.io/Product/iEthernet/W5500/w5500-evb-pico) board.

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_futures::{join::join3, yield_now};
use embassy_net::{Stack, StackResources};
use embassy_net_wiznet::chip::W5500;
use embassy_net_wiznet::{<PERSON><PERSON>, Runner, State as WizState};
use embassy_rp::bind_interrupts;
use embassy_rp::clocks::RoscRng;
use embassy_rp::gpio::{Input, Level, Output, Pull};
use embassy_rp::peripherals::{SPI0, USB};
use embassy_rp::spi::{Async, Config as SpiConfig, Spi};
use embassy_rp::usb::{Driver, InterruptHandler};
use embassy_time::{Delay, Duration};
use embassy_usb::class::cdc_acm::{CdcAcmClass, State as UsbState};
use embassy_usb::{Builder, Config};
use embedded_hal_bus::spi::ExclusiveDevice;
use embedded_io_async::Write;
use static_cell::StaticCell;
use {defmt_rtt as _, panic_probe as _};

bind_interrupts!(struct Irqs {
    USBCTRL_IRQ => InterruptHandler<USB>;
});

#[embassy_executor::task]
async fn ethernet_task(
    runner: Runner<
        'static,
        W5500,
        ExclusiveDevice<Spi<'static, SPI0, Async>, Output<'static>, Delay>,
        Input<'static>,
        Output<'static>,
    >,
) -> ! {
    runner.run().await
}

#[embassy_executor::task]
async fn net_task(mut runner: embassy_net::Runner<'static, Device<'static>>) -> ! {
    runner.run().await
}

#[embassy_executor::main]
async fn main(spawner: Spawner) {
    log::info!("Starting serverpi...");
    
    let p = embassy_rp::init(Default::default());
    let mut rng = RoscRng;
    let mut led = Output::new(p.PIN_25, Level::Low);

    // Setup USB Serial Logger
    let driver = Driver::new(p.USB, Irqs);

    // Create embassy-usb Config
    let mut usb_config = Config::new(0xc0de, 0xcafe);
    usb_config.manufacturer = Some("Embassy");
    usb_config.product = Some("ServerPi Ethernet Device");
    usb_config.serial_number = Some("12345678");
    usb_config.max_power = 100;
    usb_config.max_packet_size_0 = 64;

    // Create embassy-usb DeviceBuilder using the driver and config
    let mut config_descriptor = [0; 256];
    let mut bos_descriptor = [0; 256];
    let mut control_buf = [0; 64];
    
    let mut logger_state = UsbState::new();

    let mut builder = Builder::new(
        driver,
        usb_config,
        &mut config_descriptor,
        &mut bos_descriptor,
        &mut [], // no msos descriptors
        &mut control_buf,
    );

    // Create a class for the logger
    let logger_class = CdcAcmClass::new(&mut builder, &mut logger_state, 64);

    // Creates the logger and returns the logger future
    let log_fut = embassy_usb_logger::with_class!(1024, log::LevelFilter::Info, logger_class);

    // Build the USB device
    let mut usb = builder.build();

    // Setup Ethernet
    let mut spi_cfg = SpiConfig::default();
    spi_cfg.frequency = 50_000_000;
    let (miso, mosi, clk) = (p.PIN_16, p.PIN_19, p.PIN_18);
    let spi = Spi::new(p.SPI0, clk, mosi, miso, p.DMA_CH0, p.DMA_CH1, spi_cfg);
    let cs = Output::new(p.PIN_17, Level::High);
    let w5500_int = Input::new(p.PIN_21, Pull::Up);
    let w5500_reset = Output::new(p.PIN_20, Level::High);

    let mac_addr = [0x02, 0x00, 0x00, 0x00, 0x00, 0x00];
    static NET_STATE: StaticCell<WizState<8, 8>> = StaticCell::new();
    let net_state = NET_STATE.init(WizState::<8, 8>::new());
    let (device, eth_runner) = embassy_net_wiznet::new(
        mac_addr,
        net_state,
        ExclusiveDevice::new(spi, cs, Delay),
        w5500_int,
        w5500_reset,
    )
    .await
    .unwrap();
    unwrap!(spawner.spawn(ethernet_task(eth_runner)));

    // Generate random seed
    let seed = rng.next_u64();

    // Init network stack
    static RESOURCES: StaticCell<StackResources<3>> = StaticCell::new();
    let (stack, net_runner) = embassy_net::new(
        device,
        embassy_net::Config::dhcpv4(Default::default()),
        RESOURCES.init(StackResources::new()),
        seed,
    );

    // Launch network task
    unwrap!(spawner.spawn(net_task(net_runner)));

    // TCP server task
    let tcp_task = async {
        log::info!("Waiting for DHCP...");
        let cfg = wait_for_config(stack).await;
        let local_addr = cfg.address.address();
        log::info!("IP address: {:?}", local_addr);

        let mut rx_buffer = [0; 4096];
        let mut tx_buffer = [0; 4096];
        let mut buf = [0; 4096];
        loop {
            let mut socket = embassy_net::tcp::TcpSocket::new(stack, &mut rx_buffer, &mut tx_buffer);
            socket.set_timeout(Some(Duration::from_secs(10)));

            led.set_low();
            log::info!("Listening on TCP:1234...");
            if let Err(e) = socket.accept(1234).await {
                log::warn!("accept error: {:?}", e);
                continue;
            }
            log::info!("Received connection from {:?}", socket.remote_endpoint());
            led.set_high();

            loop {
                let n = match socket.read(&mut buf).await {
                    Ok(0) => {
                        log::warn!("read EOF");
                        break;
                    }
                    Ok(n) => n,
                    Err(e) => {
                        log::warn!("{:?}", e);
                        break;
                    }
                };
                log::info!("rxd {}", core::str::from_utf8(&buf[..n]).unwrap());

                if let Err(e) = socket.write_all(&buf[..n]).await {
                    log::warn!("write error: {:?}", e);
                    break;
                }
            }
        }
    };

    // Run USB device task
    let usb_fut = usb.run();

    // Run everything concurrently
    join3(usb_fut, tcp_task, log_fut).await;
}

async fn wait_for_config(stack: Stack<'static>) -> embassy_net::StaticConfigV4 {
    loop {
        if let Some(config) = stack.config_v4() {
            return config.clone();
        }
        yield_now().await;
    }
}
