//! Catapult Controller State Machine
//!
//! Manages the catapult states: Disarmed -> Armed -> Launching -> Disarmed
//! Controls GPIO pins for armed and launch indicators

use defmt::*;
use embassy_rp::gpio::Output;
use embassy_sync::{blocking_mutex::raw::ThreadModeRawMutex, mutex::Mutex};
use embassy_time::{Duration, Instant};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum CatapultState {
    Disarmed,
    Armed,
    Launching,
}

impl CatapultState {
    pub fn as_str(&self) -> &'static str {
        match self {
            CatapultState::Disarmed => "disarmed",
            CatapultState::Armed => "armed",
            CatapultState::Launching => "launching",
        }
    }
}

pub struct CatapultController {
    state: CatapultState,
    armed_pin: Output<'static>,  // PIN_25
    launch_pin: Output<'static>, // PIN_10
    launch_start_time: Option<Instant>,
    launch_led_state: bool,
}

impl CatapultController {
    pub fn new(armed_pin: Output<'static>, launch_pin: Output<'static>) -> Self {
        let mut controller = Self {
            state: CatapultState::Disarmed,
            armed_pin,
            launch_pin,
            launch_start_time: None,
            launch_led_state: false,
        };
        controller.update_pins();
        controller
    }

    pub fn get_state(&self) -> CatapultState {
        self.state
    }

    pub fn arm(&mut self) -> Result<(), &'static str> {
        match self.state {
            CatapultState::Disarmed => {
                self.state = CatapultState::Armed;
                self.update_pins();
                info!("Catapult ARMED");
                Ok(())
            }
            _ => Err("Cannot arm: not in disarmed state"),
        }
    }

    pub fn launch(&mut self) -> Result<(), &'static str> {
        match self.state {
            CatapultState::Armed => {
                self.state = CatapultState::Launching;
                self.launch_start_time = Some(Instant::now());
                self.launch_led_state = false;
                self.update_pins();
                info!("Catapult LAUNCHING - Motor started");
                Ok(())
            }
            CatapultState::Launching => Err("Already launching"),
            CatapultState::Disarmed => Err("Cannot launch: not armed"),
        }
    }

    pub fn stop(&mut self) -> Result<&'static str, &'static str> {
        match self.state {
            CatapultState::Armed => {
                self.state = CatapultState::Disarmed;
                self.launch_start_time = None;
                self.update_pins();
                info!("Catapult DISARMED");
                Ok("Catapult disarmed")
            }
            CatapultState::Launching => {
                self.state = CatapultState::Disarmed;
                self.launch_start_time = None;
                self.update_pins();
                info!("Catapult LAUNCH ABORTED - Motor stopped");
                Ok("Launch aborted")
            }
            CatapultState::Disarmed => Err("Already disarmed"),
        }
    }

    fn update_pins(&mut self) {
        match self.state {
            CatapultState::Disarmed => {
                self.armed_pin.set_low();
                self.launch_pin.set_low();
            }
            CatapultState::Armed => {
                self.armed_pin.set_high();
                self.launch_pin.set_low();
            }
            CatapultState::Launching => {
                self.armed_pin.set_low();
                // For launching, we'll control the pin in the blink function
                // to simulate motor PWM control
                if self.launch_led_state {
                    self.launch_pin.set_high();
                } else {
                    self.launch_pin.set_low();
                }
            }
        }
    }

    /// Updates the launch cycle and returns true if state changed
    pub fn update_launch_cycle(&mut self) -> bool {
        if let CatapultState::Launching = self.state {
            if let Some(start_time) = self.launch_start_time {
                let elapsed = start_time.elapsed();

                // Launch sequence lasts 10 seconds
                if elapsed >= Duration::from_secs(10) {
                    // Auto-complete launch sequence
                    self.state = CatapultState::Disarmed;
                    self.launch_start_time = None;
                    self.update_pins();
                    info!("Catapult LAUNCH COMPLETE - Motor stopped");
                    return true;
                }

                // Blink LED every 200ms to simulate motor operation
                let blink_cycle = elapsed.as_millis() / 200;
                let new_led_state = (blink_cycle % 2) == 0;

                if new_led_state != self.launch_led_state {
                    self.launch_led_state = new_led_state;
                    self.update_pins();
                }
            }
        }
        false
    }
}

pub type SharedController = Mutex<ThreadModeRawMutex, CatapultController>;
