//! Configuration module for ServerPi
//!
//! Provides network configuration options including static IP and DHCP

use embassy_net::{Config, DhcpConfig, Ipv4Address, Ipv4Cidr, StaticConfigV4};
use heapless::Vec;

/// Network configuration options
#[derive(Clone, Debug)]
pub enum NetworkConfig {
    /// Use DHCP for automatic IP configuration
    Dhcp(DhcpConfig),
    /// Use static IP configuration
    Static(StaticIpConfig),
}

/// Static IP configuration parameters
#[derive(Clone, Debug)]
pub struct StaticIpConfig {
    /// IP address with subnet mask (e.g., "***********00/24")
    pub ip_address: Ipv4Cidr,
    /// Gateway IP address (e.g., "***********")
    pub gateway: Option<Ipv4Address>,
    /// DNS servers (up to 3)
    pub dns_servers: Vec<Ipv4Address, 3>,
}

impl Default for NetworkConfig {
    fn default() -> Self {
        NetworkConfig::Dhcp(DhcpConfig::default())
    }
}

impl Default for StaticIpConfig {
    fn default() -> Self {
        let mut dns_servers = Vec::new();
        // Add Google DNS as default
        let _ = dns_servers.push(Ipv4Address::new(8, 8, 8, 8));
        let _ = dns_servers.push(Ipv4Address::new(8, 8, 4, 4));
        
        StaticIpConfig {
            ip_address: Ipv4Cidr::new(Ipv4Address::new(192, 168, 1, 100), 24),
            gateway: Some(Ipv4Address::new(192, 168, 1, 1)),
            dns_servers,
        }
    }
}

impl NetworkConfig {
    /// Create a new DHCP configuration
    pub fn dhcp() -> Self {
        NetworkConfig::Dhcp(DhcpConfig::default())
    }
    
    /// Create a new static IP configuration with default values
    pub fn static_ip() -> Self {
        NetworkConfig::Static(StaticIpConfig::default())
    }
    
    /// Create a new static IP configuration with custom values
    pub fn static_ip_custom(
        ip_address: Ipv4Cidr,
        gateway: Option<Ipv4Address>,
        dns_servers: Vec<Ipv4Address, 3>,
    ) -> Self {
        NetworkConfig::Static(StaticIpConfig {
            ip_address,
            gateway,
            dns_servers,
        })
    }
    
    /// Convert to embassy-net Config
    pub fn to_embassy_config(&self) -> Config {
        match self {
            NetworkConfig::Dhcp(dhcp_config) => Config::dhcpv4(dhcp_config.clone()),
            NetworkConfig::Static(static_config) => {
                let static_v4 = StaticConfigV4 {
                    address: static_config.ip_address,
                    gateway: static_config.gateway,
                    dns_servers: static_config.dns_servers.clone(),
                };
                Config::ipv4_static(static_v4)
            }
        }
    }
}

/// Get the global network configuration
///
/// Change this function to switch between DHCP and static IP configuration
pub fn get_network_config() -> NetworkConfig {
    // Use DHCP by default
    NetworkConfig::Dhcp(DhcpConfig::default())

    // Uncomment the following lines to use static IP instead:
    // NetworkConfig::static_ip_custom(
    //     Ipv4Cidr::new(Ipv4Address::new(192, 168, 1, 100), 24),
    //     Some(Ipv4Address::new(192, 168, 1, 1)),
    //     {
    //         let mut dns = Vec::new();
    //         let _ = dns.push(Ipv4Address::new(8, 8, 8, 8));
    //         let _ = dns.push(Ipv4Address::new(8, 8, 4, 4));
    //         dns
    //     },
    // )
}

/// Example function showing how to create a static IP configuration
///
/// This is provided as a reference for users who want to use static IP
pub fn get_static_ip_example() -> NetworkConfig {
    let mut dns_servers = Vec::new();
    let _ = dns_servers.push(Ipv4Address::new(8, 8, 8, 8));      // Google DNS
    let _ = dns_servers.push(Ipv4Address::new(8, 8, 4, 4));      // Google DNS secondary

    NetworkConfig::static_ip_custom(
        Ipv4Cidr::new(Ipv4Address::new(192, 168, 1, 100), 24),  // IP: ***********00/24
        Some(Ipv4Address::new(192, 168, 1, 1)),                 // Gateway: ***********
        dns_servers,
    )
}

// Example static IP configuration (commented out)
// pub const NETWORK_CONFIG: NetworkConfig = NetworkConfig::Static(StaticIpConfig {
//     ip_address: Ipv4Cidr::new(Ipv4Address::new(192, 168, 1, 100), 24),
//     gateway: Some(Ipv4Address::new(192, 168, 1, 1)),
//     dns_servers: {
//         let mut dns = Vec::new();
//         let _ = dns.push(Ipv4Address::new(8, 8, 8, 8));
//         let _ = dns.push(Ipv4Address::new(8, 8, 4, 4));
//         dns
//     },
// });
