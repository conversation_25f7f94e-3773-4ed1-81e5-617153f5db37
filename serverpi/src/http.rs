//! HTTP Request Handler
//!
//! Handles HTTP API endpoints for catapult control:
//! - POST /arm - Arms the catapult
//! - POST /start - Starts launch sequence
//! - POST /stop - Stops/disarms the catapult
//! - GET /health - Returns current state

use core::str;
use defmt::*;
use embassy_net::tcp::TcpSocket;
use embassy_time::Timer;
use embedded_io_async::Write;
use heapless::{String, Vec};

use crate::catapult::{CatapultState, SharedController};

pub async fn handle_http_request(
    socket: &mut TcpSocket<'_>,
    buf: &mut [u8],
    catapult: &SharedController,
) -> Result<(), &'static str> {
    // Read the complete HTTP request
    let mut total_read = 0;
    loop {
        let n = socket
            .read(&mut buf[total_read..])
            .await
            .map_err(|_| "Read error")?;
        if n == 0 {
            break;
        }
        total_read += n;

        // Check if we have a complete HTTP request (ends with \r\n\r\n)
        let request_str = str::from_utf8(&buf[..total_read]).map_err(|_| "Invalid UTF-8")?;
        if request_str.contains("\r\n\r\n") {
            break;
        }

        // Prevent buffer overflow
        if total_read >= buf.len() - 100 {
            break;
        }
    }

    if total_read == 0 {
        return Err("Empty request");
    }

    let request = str::from_utf8(&buf[..total_read]).map_err(|_| "Invalid UTF-8")?;
    info!("HTTP Request: {}", request);

    // Parse HTTP request line
    let lines: Vec<&str, 10> = request.lines().collect();
    if lines.is_empty() {
        return Err("No request line");
    }

    let request_line = lines[0];
    let parts: Vec<&str, 3> = request_line.split_whitespace().collect();
    if parts.len() < 2 {
        return Err("Invalid request line");
    }

    let method = parts[0];
    let path = parts[1];

    info!("Method: {}, Path: {}", method, path);

    let response = match (method, path) {
        ("POST", "/arm") => {
            let mut controller = catapult.lock().await;
            match controller.arm() {
                Ok(()) => create_response(
                    "200 OK",
                    "application/json",
                    "{\"message\":\"Catapult armed successfully\"}",
                ),
                Err(e) => {
                    create_response("400 Bad Request", "application/json", &format_json_error(e))
                }
            }
        }
        ("POST", "/start") => {
            let mut controller = catapult.lock().await;
            match controller.launch() {
                Ok(()) => create_response(
                    "200 OK",
                    "application/json",
                    "{\"message\":\"Launch started\"}",
                ),
                Err(e) => {
                    create_response("400 Bad Request", "application/json", &format_json_error(e))
                }
            }
        }
        ("POST", "/stop") => {
            let mut controller = catapult.lock().await;
            match controller.stop() {
                Ok(message) => {
                    let response_body = format_json_message(message);
                    create_response("200 OK", "application/json", &response_body)
                }
                Err(e) => {
                    create_response("400 Bad Request", "application/json", &format_json_error(e))
                }
            }
        }
        ("GET", "/health") => {
            let controller = catapult.lock().await;
            let state = controller.get_state();
            let health_json = format_health_response(state);
            create_response("200 OK", "application/json", &health_json)
        }
        _ => create_response("404 Not Found", "text/plain", "Not Found"),
    };

    info!("Sending response: {}", &response[..response.len().min(100)]);

    // Send the response and ensure it's flushed
    if let Err(e) = socket.write_all(response.as_bytes()).await {
        error!("Write error: {:?}", e);
        return Err("Write error");
    }

    // Give some time for the response to be sent before closing
    Timer::after_millis(10).await;

    info!("Response sent successfully");
    Ok(())
}

fn create_response(status: &str, content_type: &str, body: &str) -> String<512> {
    let mut response = String::new();
    let _ = response.push_str("HTTP/1.1 ");
    let _ = response.push_str(status);
    let _ = response.push_str("\r\nContent-Type: ");
    let _ = response.push_str(content_type);
    let _ = response.push_str("\r\nContent-Length: ");

    // Convert body length to string manually since to_string() isn't available in no_std
    let body_len = body.len();
    let mut len_str = String::<16>::new();
    let mut temp = body_len;
    if temp == 0 {
        let _ = len_str.push('0');
    } else {
        let mut digits = Vec::<char, 16>::new();
        while temp > 0 {
            let _ = digits.push((b'0' + (temp % 10) as u8) as char);
            temp /= 10;
        }
        for digit in digits.iter().rev() {
            let _ = len_str.push(*digit);
        }
    }

    let _ = response.push_str(&len_str);
    let _ = response.push_str("\r\nConnection: close\r\n\r\n");
    let _ = response.push_str(body);
    response
}

fn format_json_message(message: &str) -> String<256> {
    let mut json = String::new();
    let _ = json.push_str("{\"message\":\"");
    let _ = json.push_str(message);
    let _ = json.push_str("\"}");
    json
}

fn format_json_error(error: &str) -> String<256> {
    let mut json = String::new();
    let _ = json.push_str("{\"error\":\"");
    let _ = json.push_str(error);
    let _ = json.push_str("\"}");
    json
}

fn format_health_response(state: CatapultState) -> String<256> {
    let mut json = String::new();
    let _ = json.push_str("{\"state\":\"");
    let _ = json.push_str(state.as_str());
    let _ = json.push_str("\",\"end_switch_closed\":false}");
    json
}
