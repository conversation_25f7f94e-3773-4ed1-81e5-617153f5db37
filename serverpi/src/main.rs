//! Catapult Control System with HTTP API and State Machine
//! Implements Armed -> Launched -> Disarmed state cycle
//! PIN_25: Armed indicator (high when armed)
//! PIN_10: Launch indicator (high when launched)

#![no_std]
#![no_main]

use defmt::*;
use embassy_executor::Spawner;
use embassy_futures::{join::join4, yield_now};
use embassy_net::{Stack, StackResources};
use embassy_net_wiznet::chip::W5500;
use embassy_net_wiznet::{<PERSON><PERSON>, Runner, State as WizState};
use embassy_rp::bind_interrupts;
use embassy_rp::clocks::RoscRng;
use embassy_rp::gpio::{Input, Level, Output, Pull};
use embassy_rp::peripherals::{SPI0, USB};
use embassy_rp::spi::{Async, Config as SpiConfig, Spi};
use embassy_rp::usb::{Driver, InterruptHandler};
use embassy_time::{Delay, Duration};
use embassy_usb::class::cdc_acm::{CdcAcmClass, State as UsbState};
use embassy_usb::{Builder, Config};
use embedded_hal_bus::spi::ExclusiveDevice;
use static_cell::StaticCell;
use {defmt_rtt as _, panic_probe as _};

mod catapult;
mod config;
mod http;
mod network;

use catapult::{CatapultController, SharedController};
use config::get_network_config;
use http::handle_http_request;
use network::wait_for_config;

bind_interrupts!(struct Irqs {
    USBCTRL_IRQ => InterruptHandler<USB>;
});

static CATAPULT: StaticCell<SharedController> = StaticCell::new();

#[embassy_executor::task]
async fn ethernet_task(
    runner: Runner<
        'static,
        W5500,
        ExclusiveDevice<Spi<'static, SPI0, Async>, Output<'static>, Delay>,
        Input<'static>,
        Output<'static>,
    >,
) -> ! {
    runner.run().await
}

#[embassy_executor::task]
async fn net_task(mut runner: embassy_net::Runner<'static, Device<'static>>) -> ! {
    runner.run().await
}

#[embassy_executor::task]
async fn state_machine_task(catapult: &'static SharedController) -> ! {
    use embassy_time::Timer;

    loop {
        {
            let mut controller = catapult.lock().await;
            let _state_changed = controller.update_launch_cycle();
        }
        Timer::after_millis(50).await; // Check every 50ms for responsive control
    }
}

#[embassy_executor::main]
async fn main(spawner: Spawner) {
    log::info!("Starting Catapult Control System...");

    let p = embassy_rp::init(Default::default());
    let mut rng = RoscRng;

    // Initialize catapult controller with GPIO pins
    let armed_pin = Output::new(p.PIN_25, Level::Low); // Armed indicator
    let launch_pin = Output::new(p.PIN_10, Level::Low); // Launch indicator
    let controller = CatapultController::new(armed_pin, launch_pin);
    let catapult = CATAPULT.init(embassy_sync::mutex::Mutex::new(controller));

    // Setup USB Serial Logger
    let driver = Driver::new(p.USB, Irqs);
    let mut usb_config = Config::new(0xc0de, 0xcafe);
    usb_config.manufacturer = Some("Embassy");
    usb_config.product = Some("Catapult Controller");
    usb_config.serial_number = Some("12345678");
    usb_config.max_power = 100;
    usb_config.max_packet_size_0 = 64;

    let mut config_descriptor = [0; 256];
    let mut bos_descriptor = [0; 256];
    let mut control_buf = [0; 64];
    let mut logger_state = UsbState::new();

    let mut builder = Builder::new(
        driver,
        usb_config,
        &mut config_descriptor,
        &mut bos_descriptor,
        &mut [],
        &mut control_buf,
    );

    let logger_class = CdcAcmClass::new(&mut builder, &mut logger_state, 64);
    let log_fut = embassy_usb_logger::with_class!(1024, log::LevelFilter::Info, logger_class);
    let mut usb = builder.build();

    // Setup Ethernet
    let mut spi_cfg = SpiConfig::default();
    spi_cfg.frequency = 50_000_000;
    let (miso, mosi, clk) = (p.PIN_16, p.PIN_19, p.PIN_18);
    let spi = Spi::new(p.SPI0, clk, mosi, miso, p.DMA_CH0, p.DMA_CH1, spi_cfg);
    let cs = Output::new(p.PIN_17, Level::High);
    let w5500_int = Input::new(p.PIN_21, Pull::Up);
    let w5500_reset = Output::new(p.PIN_20, Level::High);

    let mac_addr = [0x02, 0x00, 0x00, 0x00, 0x00, 0x00];
    static NET_STATE: StaticCell<WizState<8, 8>> = StaticCell::new();
    let net_state = NET_STATE.init(WizState::<8, 8>::new());
    let (device, eth_runner) = embassy_net_wiznet::new(
        mac_addr,
        net_state,
        ExclusiveDevice::new(spi, cs, Delay),
        w5500_int,
        w5500_reset,
    )
    .await
    .unwrap();
    unwrap!(spawner.spawn(ethernet_task(eth_runner)));

    // Generate random seed
    let seed = rng.next_u64();

    // Init network stack
    static RESOURCES: StaticCell<StackResources<3>> = StaticCell::new();
    let (stack, net_runner) = embassy_net::new(
        device,
        get_network_config().to_embassy_config(),
        RESOURCES.init(StackResources::new()),
        seed,
    );

    unwrap!(spawner.spawn(net_task(net_runner)));
    unwrap!(spawner.spawn(state_machine_task(catapult)));

    // HTTP server task
    let http_task = async {
        log::info!("Waiting for network configuration...");
        let cfg = wait_for_config(stack).await;
        let local_addr = cfg.address.address();
        log::info!("IP address: {:?}", local_addr);
        log::info!("HTTP API available at: http://{:?}", local_addr);

        let mut rx_buffer = [0; 4096];
        let mut tx_buffer = [0; 4096];
        let mut buf = [0; 4096];

        loop {
            let mut socket =
                embassy_net::tcp::TcpSocket::new(stack, &mut rx_buffer, &mut tx_buffer);
            socket.set_timeout(Some(Duration::from_secs(10)));

            log::info!("Listening on HTTP:80...");
            if let Err(e) = socket.accept(80).await {
                log::warn!("accept error: {:?}", e);
                continue;
            }
            log::info!("HTTP connection from {:?}", socket.remote_endpoint());

            if let Err(e) = handle_http_request(&mut socket, &mut buf, catapult).await {
                log::warn!("HTTP request error: {:?}", e);
            }
        }
    };

    let usb_fut = usb.run();
    join4(usb_fut, http_task, log_fut, core::future::pending::<()>()).await;
}
