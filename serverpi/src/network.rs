//! Network Utilities
//!
//! Provides network-related helper functions

use embassy_futures::yield_now;
use embassy_net::Stack;

/// Wait for DHCP configuration to be available
pub async fn wait_for_config(stack: Stack<'static>) -> embassy_net::StaticConfigV4 {
    loop {
        if let Some(config) = stack.config_v4() {
            return config.clone();
        }
        yield_now().await;
    }
}
