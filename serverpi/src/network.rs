//! Network Utilities
//!
//! Provides network-related helper functions

use embassy_futures::yield_now;
use embassy_net::Stack;

/// Wait for network configuration to be available
///
/// For DHCP configurations, this waits until DHCP assigns an IP address.
/// For static configurations, this should return immediately once the stack is initialized.
pub async fn wait_for_config(stack: Stack<'static>) -> embassy_net::StaticConfigV4 {
    loop {
        if let Some(config) = stack.config_v4() {
            return config.clone();
        }
        yield_now().await;
    }
}
