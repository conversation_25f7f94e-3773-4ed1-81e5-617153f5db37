{"rustc_fingerprint": 2938508929845333125, "outputs": {"5110402071386716506": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.88.0 (6b00bc388 2025-06-23)\nbinary: rustc\ncommit-hash: 6b00bc3880198600130e1cf62b8f8a93494488cc\ncommit-date: 2025-06-23\nhost: x86_64-unknown-linux-gnu\nrelease: 1.88.0\nLLVM version: 20.1.5\n", "stderr": ""}, "14981819464525939669": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.so\nlib___.so\nlib___.a\nlib___.so\n/home/<USER>/.rustup/toolchains/1.88-x86_64-unknown-linux-gnu\noff\npacked\nunpacked\n___\ndebug_assertions\npanic=\"unwind\"\nproc_macro\ntarget_abi=\"\"\ntarget_arch=\"x86_64\"\ntarget_endian=\"little\"\ntarget_env=\"gnu\"\ntarget_family=\"unix\"\ntarget_feature=\"fxsr\"\ntarget_feature=\"sse\"\ntarget_feature=\"sse2\"\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_os=\"linux\"\ntarget_pointer_width=\"64\"\ntarget_vendor=\"unknown\"\nunix\n", "stderr": ""}, "14526741332122104816": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/home/<USER>/.rustup/toolchains/1.88-x86_64-unknown-linux-gnu\noff\n___\ndebug_assertions\npanic=\"abort\"\nproc_macro\ntarget_abi=\"eabi\"\ntarget_arch=\"arm\"\ntarget_endian=\"little\"\ntarget_env=\"\"\ntarget_os=\"none\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"unknown\"\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `thumbv6m-none-eabi`\n\nwarning: dropping unsupported crate type `cdylib` for target `thumbv6m-none-eabi`\n\nwarning: dropping unsupported crate type `proc-macro` for target `thumbv6m-none-eabi`\n\nwarning: 3 warnings emitted\n\n"}}, "successes": {}}