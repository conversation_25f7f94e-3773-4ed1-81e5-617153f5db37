{"rustc": 11410426090777951712, "features": "[\"_arch\", \"arch-cortex-m\", \"defmt\", \"executor-interrupt\", \"executor-thread\"]", "declared_features": "[\"_arch\", \"_timer-item-payload\", \"arch-avr\", \"arch-cortex-ar\", \"arch-cortex-m\", \"arch-riscv32\", \"arch-spin\", \"arch-std\", \"arch-wasm\", \"defmt\", \"executor-interrupt\", \"executor-thread\", \"log\", \"nightly\", \"rtos-trace\", \"timer-item-payload-size-1\", \"timer-item-payload-size-2\", \"timer-item-payload-size-4\", \"timer-item-payload-size-8\", \"trace\", \"turbowakers\"]", "target": 5408242616063297496, "profile": 15657897354478470176, "path": 12620963301482498257, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/embassy-executor-055caf69452db607/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}