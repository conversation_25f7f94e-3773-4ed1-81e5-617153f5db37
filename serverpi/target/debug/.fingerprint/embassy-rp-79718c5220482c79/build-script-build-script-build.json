{"rustc": 11410426090777951712, "features": "[\"critical-section-impl\", \"default\", \"defmt\", \"rp2040\", \"rt\", \"time-driver\", \"unstable-pac\"]", "declared_features": "[\"_rp235x\", \"_test\", \"binary-info\", \"boot2-at25sf128a\", \"boot2-gd25q64cs\", \"boot2-generic-03h\", \"boot2-is25lp080\", \"boot2-none\", \"boot2-ram-memcpy\", \"boot2-w25q080\", \"boot2-w25x10cl\", \"chrono\", \"critical-section-impl\", \"default\", \"defmt\", \"imagedef-none\", \"imagedef-nonsecure-exe\", \"imagedef-secure-exe\", \"intrinsics\", \"log\", \"qspi-as-gpio\", \"rom-func-cache\", \"rom-v2-intrinsics\", \"rp2040\", \"rp235xa\", \"rp235xb\", \"rt\", \"run-from-ram\", \"time-driver\", \"unstable-pac\"]", "target": 5408242616063297496, "profile": 15657897354478470176, "path": 16074732192729097731, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/embassy-rp-79718c5220482c79/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}