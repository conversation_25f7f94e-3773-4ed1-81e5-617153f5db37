{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"__trybuild\", \"cas\", \"default\", \"serde\", \"ufmt-impl\", \"ufmt-write\", \"x86-sync-pool\"]", "target": 17883862002600103897, "profile": 15657897354478470176, "path": 12477807267863276910, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/heapless-520252c20a87104e/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}