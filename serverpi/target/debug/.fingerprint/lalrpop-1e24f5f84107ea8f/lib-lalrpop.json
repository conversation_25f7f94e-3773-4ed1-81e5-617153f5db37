{"rustc": 11410426090777951712, "features": "[\"default\", \"lexer\", \"pico-args\", \"unicode\"]", "declared_features": "[\"default\", \"lexer\", \"pico-args\", \"unicode\"]", "target": 7230023195380417589, "profile": 15657897354478470176, "path": 6905145788638383059, "deps": [[2256306810175054172, "petgraph", false, 7739104201472570326], [3193035971919077392, "pico_args", false, 17948435016243001060], [3791929332532787956, "string_cache", false, 10548434370030898099], [4347453481182222290, "term", false, 13862084144606055084], [8359691489448390209, "ascii_canvas", false, 1056701722818561014], [9408802513701742484, "regex_syntax", false, 794822811429650723], [9451456094439810778, "regex", false, 13151804126648956510], [9519969280819313548, "bit_set", false, 14219494176728781102], [9862128373186485034, "lalrpop_util", false, 4733429981082567502], [11017232866922121725, "sha3", false, 1165259252501910583], [14834971194847821903, "ena", false, 6049610362479209836], [15622660310229662834, "walkdir", false, 14282618360911061676], [16126285161989458480, "unicode_xid", false, 5795886168645504505], [16326338539882746041, "itertools", false, 14861084368678471271]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/lalrpop-1e24f5f84107ea8f/dep-lib-lalrpop", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}