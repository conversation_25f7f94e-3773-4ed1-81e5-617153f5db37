cargo:rustc-check-cfg=cfg(fuzzing)
cargo:rustc-check-cfg=cfg(no_is_available)
cargo:rustc-check-cfg=cfg(no_literal_byte_character)
cargo:rustc-check-cfg=cfg(no_literal_c_string)
cargo:rustc-check-cfg=cfg(no_source_text)
cargo:rustc-check-cfg=cfg(proc_macro_span)
cargo:rustc-check-cfg=cfg(proc_macro_span_file)
cargo:rustc-check-cfg=cfg(proc_macro_span_location)
cargo:rustc-check-cfg=cfg(procmacro2_backtrace)
cargo:rustc-check-cfg=cfg(procmacro2_build_probe)
cargo:rustc-check-cfg=cfg(procmacro2_nightly_testing)
cargo:rustc-check-cfg=cfg(procmacro2_semver_exempt)
cargo:rustc-check-cfg=cfg(randomize_layout)
cargo:rustc-check-cfg=cfg(span_locations)
cargo:rustc-check-cfg=cfg(super_unstable)
cargo:rustc-check-cfg=cfg(wrap_proc_macro)
cargo:rustc-cfg=span_locations
cargo:rerun-if-changed=src/probe/proc_macro_span.rs
cargo:rustc-cfg=wrap_proc_macro
cargo:rerun-if-changed=src/probe/proc_macro_span_location.rs
cargo:rustc-cfg=proc_macro_span_location
cargo:rerun-if-changed=src/probe/proc_macro_span_file.rs
cargo:rustc-cfg=proc_macro_span_file
cargo:rerun-if-env-changed=RUSTC_BOOTSTRAP
