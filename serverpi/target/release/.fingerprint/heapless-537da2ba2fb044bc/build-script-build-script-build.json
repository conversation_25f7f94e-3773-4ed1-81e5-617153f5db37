{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"__trybuild\", \"cas\", \"default\", \"serde\", \"ufmt-impl\", \"ufmt-write\", \"x86-sync-pool\"]", "target": 17883862002600103897, "profile": 3182989938650469896, "path": 12477807267863276910, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/heapless-537da2ba2fb044bc/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}