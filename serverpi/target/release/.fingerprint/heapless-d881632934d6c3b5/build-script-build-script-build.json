{"rustc": 11410426090777951712, "features": "[\"atomic-polyfill\", \"cas\", \"default\"]", "declared_features": "[\"__trybuild\", \"atomic-polyfill\", \"cas\", \"default\", \"defmt\", \"defmt-impl\", \"mpmc_large\", \"serde\", \"ufmt-impl\", \"ufmt-write\", \"x86-sync-pool\"]", "target": 17883862002600103897, "profile": 3182989938650469896, "path": 15750157600059307629, "deps": [[8576480473721236041, "rustc_version", false, 14344121261144301355]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/heapless-d881632934d6c3b5/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}