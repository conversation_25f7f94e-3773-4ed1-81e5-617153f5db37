{"$message_type":"diagnostic","message":"can't find crate for `test`","code":{"code":"E0463","explanation":"A crate was declared but cannot be found.\n\nErroneous code example:\n\n```compile_fail,E0463\nextern crate foo; // error: can't find crate\n```\n\nYou need to link your code to the relevant crate in order to be able to use it\n(through Cargo or the `-L` option of rustc, for example).\n\n## Common causes\n\n- The crate is not present at all. If using Cargo, add it to `[dependencies]`\n  in Cargo.toml.\n- The crate is present, but under a different name. If using Cargo, look for\n  `package = ` under `[dependencies]` in Cargo.toml.\n\n## Common causes for missing `std` or `core`\n\n- You are cross-compiling for a target which doesn't have `std` prepackaged.\n  Consider one of the following:\n  + Adding a pre-compiled version of std with `rustup target add`\n  + Building std from source with `cargo build -Z build-std`\n  + Using `#![no_std]` at the crate root, so you won't need `std` in the first\n    place.\n- You are developing the compiler itself and haven't built libstd from source.\n  You can usually build it with `x.py build library/std`. More information\n  about x.py is available in the [rustc-dev-guide].\n\n[rustc-dev-guide]: https://rustc-dev-guide.rust-lang.org/building/how-to-build-and-run.html#building-the-compiler\n"},"level":"error","spans":[{"file_name":"src/bin/mono.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[],"label":"can't find crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0463]\u001b[0m\u001b[0m\u001b[1m: can't find crate for `test`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0463`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0463`.\u001b[0m\n"}
